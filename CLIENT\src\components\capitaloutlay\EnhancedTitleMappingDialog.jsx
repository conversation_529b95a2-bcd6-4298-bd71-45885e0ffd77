import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Box,
  Typography,
  Chip,
  Stack,
  useTheme,
  alpha,
  MenuItem,
  Alert,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  MdCategory,
  MdAccountBalance,
  MdCode,
  MdSave,
  MdAdd
} from "react-icons/md";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import api from "../../config/api";

// Validation schema
const titleMappingSchema = yup.object().shape({
  sublineItem: yup.string().required("Subline Item is required"),
  accountingTitle: yup.string().required("Accounting Title is required"),
  uacsCode: yup.string()
    .required("UACS Code is required")
    .matches(/^[0-9-]+$/, "UACS Code must contain only numbers and dashes"),
  accountClass: yup.string().required("Account Class is required"),
  lineItem: yup.string().required("Line Item is required")
});

// Predefined options
const accountClasses = ["Asset", "Liability", "Equity", "Revenue", "Expense"];
const lineItems = [
  "Capital Outlays",
  "Property, Plant and Equipment Outlay",
  "Infrastructure Outlay",
  "Building and Other Structures",
  "Machinery and Equipment",
  "Transportation Equipment",
  "Furniture, Fixtures and Books",
  "Land",
  "Land Improvements"
];

const sublineItems = [
  "Infrastructure Outlay",
  "Building and Other Structures", 
  "Buildings and Other Structures",
  "Machinery and Equipment Outlay",
  "Transportation Equipment Outlay",
  "Furniture, Fixtures and Books Outlay",
  "Furniture Fixture and Books",
  "Land",
  "Land Improvements"
];

const EnhancedTitleMappingDialog = ({ open, onClose, editData = null }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const isEditing = Boolean(editData);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(titleMappingSchema),
    defaultValues: {
      sublineItem: editData?.sublineItem || "",
      accountingTitle: editData?.accountingTitle || "",
      uacsCode: editData?.uacsCode || "",
      accountClass: editData?.accountClass || "Asset",
      lineItem: editData?.lineItem || "Capital Outlays"
    }
  });

  // Watch form values for real-time preview
  const watchedValues = watch();

  // Reset form when editData changes
  useEffect(() => {
    if (editData) {
      reset({
        sublineItem: editData.sublineItem,
        accountingTitle: editData.accountingTitle,
        uacsCode: editData.uacsCode,
        accountClass: editData.accountClass,
        lineItem: editData.lineItem
      });
    } else {
      reset({
        sublineItem: "",
        accountingTitle: "",
        uacsCode: "",
        accountClass: "Asset",
        lineItem: "Capital Outlays"
      });
    }
  }, [editData, reset]);

  // Mutation for saving title mapping
  const mutation = useMutation({
    mutationFn: async (data) => {
      const url = isEditing ? `/chart-of-accounts/${editData._id}` : "/chart-of-accounts";
      const method = isEditing ? api.put : api.post;
      const response = await method(url, data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries(["chart-of-accounts"]);
      toast.success(data.message || `Title mapping ${isEditing ? "updated" : "created"} successfully`);
      onClose();
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.error || error.message || "An error occurred";
      toast.error(errorMessage);
    }
  });

  const onSubmit = (data) => {
    mutation.mutate(data);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle 
        sx={{ 
          background: 'linear-gradient(135deg, #264524 0%, #375e38 100%)',
          color: 'white',
          py: 3,
          position: 'relative'
        }}
      >
        <Box>
          <Typography variant="subtitle1" fontWeight="bold" component="div">
            {isEditing ? "Edit Title Mapping" : "Add New Title Mapping"}
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }} component="div">
            Configure subline items, accounting titles, and UACS code mappings
          </Typography>
          {isEditing && (
            <Chip 
              label={`UACS: ${editData.uacsCode}`}
              size="small"
              sx={{ 
                position: 'absolute',
                top: 16,
                right: 16,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white'
              }}
            />
          )}
        </Box>
      </DialogTitle>

      <DialogContent 
        dividers 
        sx={{ 
          p: 0,
          backgroundColor: '#f8f9fa'
        }}
      >
        <Box sx={{ p: 3 }}>
          {/* Information Alert */}
          <Alert severity="info" sx={{ mb: 3 }}>
            Title mappings define the relationship between subline items, accounting titles, and UACS codes 
            for proper capital outlay categorization and reporting.
          </Alert>

          {/* Basic Information Accordion */}
          <Accordion 
            defaultExpanded
            sx={{
              mb: 2,
              borderRadius: 2,
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              '&:before': { display: 'none' },
              '&.Mui-expanded': {
                margin: '0 0 16px 0'
              }
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                backgroundColor: '#f8f9fa',
                borderRadius: '8px 8px 0 0',
                minHeight: 56,
                '&.Mui-expanded': {
                  minHeight: 56
                },
                '& .MuiAccordionSummary-content': {
                  alignItems: 'center'
                }
              }}
            >
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box 
                  sx={{ 
                    color: '#264524',
                    fontSize: '1.5rem',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <MdAccountBalance />
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#264524' }}>
                  Basic Mapping Information
                </Typography>
              </Stack>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 3, backgroundColor: '#ffffff' }}>
              <Grid container spacing={3}>
                {/* Account Class */}
                <Grid item xs={12} md={6}>
                  <Controller
                    name="accountClass"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label="Account Class"
                        fullWidth
                        variant="outlined"
                        error={!!errors.accountClass}
                        helperText={errors.accountClass?.message}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&:hover fieldset': {
                              borderColor: '#264524',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#264524',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#264524',
                          },
                        }}
                      >
                        {accountClasses.map((accountClass) => (
                          <MenuItem key={accountClass} value={accountClass}>
                            {accountClass}
                          </MenuItem>
                        ))}
                      </TextField>
                    )}
                  />
                </Grid>

                {/* Line Item */}
                <Grid item xs={12} md={6}>
                  <Controller
                    name="lineItem"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label="Line Item"
                        fullWidth
                        variant="outlined"
                        error={!!errors.lineItem}
                        helperText={errors.lineItem?.message}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&:hover fieldset': {
                              borderColor: '#264524',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#264524',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#264524',
                          },
                        }}
                      >
                        {lineItems.map((lineItem) => (
                          <MenuItem key={lineItem} value={lineItem}>
                            {lineItem}
                          </MenuItem>
                        ))}
                      </TextField>
                    )}
                  />
                </Grid>

                {/* Subline Item */}
                <Grid item xs={12} md={6}>
                  <Controller
                    name="sublineItem"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label="Subline Item"
                        fullWidth
                        variant="outlined"
                        error={!!errors.sublineItem}
                        helperText={errors.sublineItem?.message}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&:hover fieldset': {
                              borderColor: '#264524',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#264524',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#264524',
                          },
                        }}
                      >
                        {sublineItems.map((sublineItem) => (
                          <MenuItem key={sublineItem} value={sublineItem}>
                            {sublineItem}
                          </MenuItem>
                        ))}
                      </TextField>
                    )}
                  />
                </Grid>

                {/* UACS Code */}
                <Grid item xs={12} md={6}>
                  <Controller
                    name="uacsCode"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="UACS Code"
                        fullWidth
                        variant="outlined"
                        error={!!errors.uacsCode}
                        helperText={errors.uacsCode?.message || "Format: 5-01-01-010"}
                        placeholder="5-01-01-010"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&:hover fieldset': {
                              borderColor: '#264524',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#264524',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#264524',
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                {/* Accounting Title */}
                <Grid item xs={12}>
                  <Controller
                    name="accountingTitle"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Accounting Title"
                        fullWidth
                        variant="outlined"
                        error={!!errors.accountingTitle}
                        helperText={errors.accountingTitle?.message}
                        placeholder="Enter descriptive accounting title"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '&:hover fieldset': {
                              borderColor: '#264524',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#264524',
                            },
                          },
                          '& .MuiInputLabel-root.Mui-focused': {
                            color: '#264524',
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Preview Summary */}
          {(watchedValues.sublineItem || watchedValues.accountingTitle || watchedValues.uacsCode) && (
            <>
              <Divider sx={{ my: 3 }} />
              <Box 
                sx={{ 
                  p: 2, 
                  backgroundColor: alpha(theme.palette.info.main, 0.1),
                  borderRadius: 2,
                  border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                }}
              >
                <Typography variant="h6" color="info.main" gutterBottom>
                  Mapping Preview
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      Subline Item:
                    </Typography>
                    <Chip 
                      label={watchedValues.sublineItem || "Not selected"}
                      color="primary"
                      variant="outlined"
                      size="small"
                      icon={<MdCategory />}
                      sx={{ mt: 0.5 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      UACS Code:
                    </Typography>
                    <Chip 
                      label={watchedValues.uacsCode || "Not entered"}
                      color="success"
                      variant="filled"
                      size="small"
                      icon={<MdCode />}
                      sx={{ mt: 0.5 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      Account Class:
                    </Typography>
                    <Chip 
                      label={watchedValues.accountClass || "Not selected"}
                      color="info"
                      variant="outlined"
                      size="small"
                      sx={{ mt: 0.5 }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Accounting Title:
                    </Typography>
                    <Typography variant="body1" fontWeight="medium" sx={{ mt: 0.5 }}>
                      {watchedValues.accountingTitle || "Not entered"}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </>
          )}
        </Box>
      </DialogContent>

      <DialogActions 
        sx={{ 
          p: 3, 
          backgroundColor: '#ffffff',
          borderTop: '1px solid #e0e0e0'
        }}
      >
        <Button 
          onClick={handleClose} 
          variant="outlined"
          sx={{ mr: 2 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          variant="contained"
          disabled={mutation.isLoading}
          startIcon={mutation.isLoading ? null : isEditing ? <MdSave /> : <MdAdd />}
          sx={{
            background: 'linear-gradient(45deg, #264524 30%, #375e38 90%)',
            boxShadow: '0 3px 5px 2px rgba(38, 69, 36, 0.3)',
            px: 4,
            py: 1.5,
            '&:hover': {
              background: 'linear-gradient(45deg, #1a2f1a 30%, #2a4a2b 90%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 10px 4px rgba(38, 69, 36, 0.3)'
            },
            '&:disabled': {
              background: 'linear-gradient(45deg, #ccc 30%, #999 90%)'
            }
          }}
        >
          {mutation.isLoading ? "Saving..." : isEditing ? "Update Mapping" : "Save Mapping"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnhancedTitleMappingDialog;
