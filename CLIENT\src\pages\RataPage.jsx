import React, { useState } from "react";
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Paper,
  Stack,
  Alert,
  Divider,
  useTheme,
  alpha
} from "@mui/material";
import {
  MdGrade,
  MdAttachMoney,
  MdTrendingUp,
  MdSettings,
  MdPeople,
  MdCalculate
} from "react-icons/md";
import {
  AiOutlineEdit,
  AiOutlineEye,
  AiOutlineDownload,
  AiOutlineReload,
  AiOutlinePlus
} from "react-icons/ai";
import { useQuery } from "@tanstack/react-query";
import CustomPage from "../components/rata/CustomPage";
import api from "../config/api";

const RataPage = () => {
  const theme = useTheme();

  // Fetch RATA data for summary cards
  const { data: rataData, isLoading: rataLoading, refetch } = useQuery({
    queryKey: ["ratas"],
    queryFn: async () => {
      const response = await api.get("/ratas");
      return response.data;
    },
  });

  const handleExport = () => {
    // Export functionality
    console.log("Exporting RATA settings...");
  };

  // Enhanced schema with better formatting and actions
  const rataSchema = {
    action: {
      type: "action",
      label: "Actions",
      customRender: (row) => (
        <Stack direction="row" spacing={1}>
          <Tooltip title="Edit RATA Rate">
            <IconButton
              size="small"
              sx={{
                color: theme.palette.primary.main,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1)
                }
              }}
            >
              <AiOutlineEdit />
            </IconButton>
          </Tooltip>
          <Tooltip title="View Details">
            <IconButton
              size="small"
              sx={{
                color: theme.palette.info.main,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.info.main, 0.1)
                }
              }}
            >
              <AiOutlineEye />
            </IconButton>
          </Tooltip>
        </Stack>
      ),
    },
    SG: {
      type: "text",
      label: "Salary Grade",
      required: true,
      searchable: true,
      show: true,
      customRender: (row) => (
        <Chip
          label={`SG-${row.SG}`}
          color="primary"
          variant="outlined"
          size="small"
          icon={<MdGrade />}
        />
      )
    },
    RATA: {
      type: "number",
      label: "RATA Amount",
      required: true,
      searchable: true,
      show: true,
      customRender: (row) => (
        <Typography variant="body2" fontWeight="medium" color="success.main">
          ₱{(row.RATA || 0).toLocaleString()}
        </Typography>
      )
    },
  };

  // Summary Cards Component
  const SummaryCards = () => {
    const rataRecords = rataData?.ratas || [];
    const totalRecords = rataRecords.length;
    const totalRataAmount = rataRecords.reduce((sum, record) => sum + (record.RATA || 0), 0);
    const averageRata = totalRecords > 0 ? totalRataAmount / totalRecords : 0;
    const highestGrade = rataRecords.length > 0
      ? Math.max(...rataRecords.map(r => parseInt(r.SG) || 0))
      : 0;

    const summaryData = [
      {
        title: "Total RATA Records",
        value: totalRecords,
        icon: <MdSettings />,
        color: theme.palette.primary.main,
        bgColor: alpha(theme.palette.primary.main, 0.1)
      },
      {
        title: "Total RATA Amount",
        value: `₱${totalRataAmount.toLocaleString()}`,
        icon: <MdAttachMoney />,
        color: theme.palette.success.main,
        bgColor: alpha(theme.palette.success.main, 0.1)
      },
      {
        title: "Average RATA",
        value: `₱${Math.round(averageRata).toLocaleString()}`,
        icon: <MdCalculate />,
        color: theme.palette.info.main,
        bgColor: alpha(theme.palette.info.main, 0.1)
      },
      {
        title: "Highest Grade",
        value: highestGrade > 0 ? `SG-${highestGrade}` : "N/A",
        icon: <MdTrendingUp />,
        color: theme.palette.warning.main,
        bgColor: alpha(theme.palette.warning.main, 0.1)
      }
    ];

    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {summaryData.map((item, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                background: `linear-gradient(135deg, ${item.bgColor} 0%, ${alpha(item.color, 0.05)} 100%)`,
                border: `1px solid ${alpha(item.color, 0.2)}`,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: `0 8px 25px ${alpha(item.color, 0.3)}`,
                  border: `1px solid ${alpha(item.color, 0.4)}`
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 2,
                      backgroundColor: item.color,
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.5rem'
                    }}
                  >
                    {item.icon}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                      {item.title}
                    </Typography>
                    <Typography variant="h6" fontWeight="bold" color={item.color}>
                      {item.value}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white'
        }}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              RATA Settings
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Manage Representation and Transportation Allowance rates by salary grade
            </Typography>
          </Box>
          <Stack direction="row" spacing={2}>
            <Tooltip title="Export RATA Settings">
              <IconButton
                onClick={handleExport}
                sx={{
                  color: 'white',
                  backgroundColor: alpha('#fff', 0.1),
                  '&:hover': { backgroundColor: alpha('#fff', 0.2) }
                }}
              >
                <AiOutlineDownload />
              </IconButton>
            </Tooltip>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => refetch()}
                sx={{
                  color: 'white',
                  backgroundColor: alpha('#fff', 0.1),
                  '&:hover': { backgroundColor: alpha('#fff', 0.2) }
                }}
              >
                <AiOutlineReload />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Paper>

      {/* Summary Cards */}
      {!rataLoading && <SummaryCards />}

      {/* Alert for no RATA records */}
      {!rataLoading && (!rataData?.ratas || rataData.ratas.length === 0) && (
        <Alert severity="info" sx={{ mb: 3 }}>
          No RATA records found. Please add RATA rates for different salary grades to enable proper allowance calculations.
        </Alert>
      )}

      <Paper sx={{ p: 0, borderRadius: 2, overflow: 'hidden' }}>
        <CustomPage
          dataListName="ratas"
          schema={rataSchema}
          title=""
          description=""
          customAddElement={
            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
              <Button
                variant="contained"
                startIcon={<AiOutlinePlus />}
                sx={{
                  background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
                  boxShadow: `0 3px 5px 2px ${alpha(theme.palette.primary.main, 0.3)}`,
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: `0 6px 10px 4px ${alpha(theme.palette.primary.main, 0.3)}`
                  }
                }}
              >
                ADD RATA RATE
              </Button>
              <Button
                variant="outlined"
                onClick={handleExport}
                startIcon={<AiOutlineDownload />}
                sx={{
                  borderColor: theme.palette.primary.main,
                  color: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    borderColor: theme.palette.primary.dark
                  }
                }}
              >
                EXPORT
              </Button>
            </Stack>
          }
        />
      </Paper>
    </Box>
  );
};

export default RataPage;
