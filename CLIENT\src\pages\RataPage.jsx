import React, { useState } from "react";
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Paper,
  Stack,
  Alert,
  Divider,
  useTheme,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem
} from "@mui/material";
import {
  MdGrade,
  MdAttachMoney,
  MdTrendingUp,
  MdSettings,
  MdPeople,
  MdCalculate
} from "react-icons/md";
import {
  AiOutlineEdit,
  AiOutlineEye,
  AiOutlineDownload,
  AiOutlineReload,
  AiOutlinePlus
} from "react-icons/ai";
import { useQuery } from "@tanstack/react-query";
import CustomPage from "../components/rata/CustomPage";
import EnhancedRataDialog from "../components/rata/EnhancedRataDialog";
import api from "../config/api";

const RataPage = () => {
  const theme = useTheme();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedData, setSelectedData] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [viewData, setViewData] = useState(null);

  // Fetch RATA data for summary cards
  const { data: rataData, isLoading: rataLoading, refetch } = useQuery({
    queryKey: ["ratas"],
    queryFn: async () => {
      const response = await api.get("/ratas");
      return response.data;
    },
  });

  const handleAddNew = () => {
    setSelectedData(null);
    setDialogOpen(true);
  };

  const handleEdit = (row) => {
    setSelectedData(row);
    setDialogOpen(true);
  };

  const handleView = (row) => {
    setViewData(row);
    setViewDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedData(null);
    refetch(); // Refresh data after dialog closes
  };

  const handleViewDialogClose = () => {
    setViewDialogOpen(false);
    setViewData(null);
  };

  const handleExport = () => {
    // Export functionality - convert data to CSV
    if (!rataData?.ratas || rataData.ratas.length === 0) {
      alert("No data to export");
      return;
    }

    const csvData = [
      ["Salary Grade", "RATA Amount", "Annual RATA", "Created Date"],
      ...rataData.ratas.map(row => [
        `SG-${row.SG}`,
        row.RATA,
        row.RATA * 12,
        new Date(row.createdAt).toLocaleDateString()
      ])
    ];

    const csvContent = csvData.map(row => row.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `rata-settings-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Enhanced schema with better formatting - let CustomPage handle actions
  const rataSchema = {
    action: {
      type: "action",
      label: "Actions",
    },
    SG: {
      type: "text",
      label: "Salary Grade",
      required: true,
      searchable: true,
      show: true,
      customRender: (row) => (
        <Chip
          label={`SG-${row.SG}`}
          color="primary"
          variant="outlined"
          size="small"
          icon={<MdGrade />}
        />
      )
    },
    RATA: {
      type: "number",
      label: "RATA Amount",
      required: true,
      searchable: true,
      show: true,
      customRender: (row) => (
        <Typography variant="body2" fontWeight="medium" color="success.main">
          ₱{(row.RATA || 0).toLocaleString()}
        </Typography>
      )
    },
    createdAt: {
      type: "date",
      label: "Created Date",
      show: true,
      customRender: (row) => (
        <Typography variant="body2" color="text.secondary">
          {new Date(row.createdAt).toLocaleDateString()}
        </Typography>
      )
    },
  };

  // Custom menu options for additional actions
  const additionalMenuOptions = [
    ({ row, parentClose }) => (
      <MenuItem
        key="view-details"
        onClick={() => {
          handleView(row);
          parentClose();
        }}
        sx={{ display: "flex", gap: 1 }}
      >
        <AiOutlineEye />
        View Details
      </MenuItem>
    )
  ];

  // Summary Cards Component
  const SummaryCards = () => {
    const rataRecords = rataData?.ratas || [];
    const totalRecords = rataRecords.length;
    const totalRataAmount = rataRecords.reduce((sum, record) => sum + (record.RATA || 0), 0);
    const averageRata = totalRecords > 0 ? totalRataAmount / totalRecords : 0;
    const highestGrade = rataRecords.length > 0
      ? Math.max(...rataRecords.map(r => parseInt(r.SG) || 0))
      : 0;

    const summaryData = [
      {
        title: "Total RATA Records",
        value: totalRecords,
        icon: <MdSettings />,
        color: theme.palette.primary.main,
        bgColor: alpha(theme.palette.primary.main, 0.1)
      },
      {
        title: "Total RATA Amount",
        value: `₱${totalRataAmount.toLocaleString()}`,
        icon: <MdAttachMoney />,
        color: theme.palette.success.main,
        bgColor: alpha(theme.palette.success.main, 0.1)
      },
      {
        title: "Average RATA",
        value: `₱${Math.round(averageRata).toLocaleString()}`,
        icon: <MdCalculate />,
        color: theme.palette.info.main,
        bgColor: alpha(theme.palette.info.main, 0.1)
      },
      {
        title: "Highest Grade",
        value: highestGrade > 0 ? `SG-${highestGrade}` : "N/A",
        icon: <MdTrendingUp />,
        color: theme.palette.warning.main,
        bgColor: alpha(theme.palette.warning.main, 0.1)
      }
    ];

    return (
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {summaryData.map((item, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                background: `linear-gradient(135deg, ${item.bgColor} 0%, ${alpha(item.color, 0.05)} 100%)`,
                border: `1px solid ${alpha(item.color, 0.2)}`,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: `0 8px 25px ${alpha(item.color, 0.3)}`,
                  border: `1px solid ${alpha(item.color, 0.4)}`
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 2,
                      backgroundColor: item.color,
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.5rem'
                    }}
                  >
                    {item.icon}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                      {item.title}
                    </Typography>
                    <Typography variant="h6" fontWeight="bold" color={item.color}>
                      {item.value}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white'
        }}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              RATA Settings
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Manage Representation and Transportation Allowance rates by salary grade
            </Typography>
          </Box>
          <Stack direction="row" spacing={2}>
            <Tooltip title="Export RATA Settings">
              <IconButton
                onClick={handleExport}
                sx={{
                  color: 'white',
                  backgroundColor: alpha('#fff', 0.1),
                  '&:hover': { backgroundColor: alpha('#fff', 0.2) }
                }}
              >
                <AiOutlineDownload />
              </IconButton>
            </Tooltip>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => refetch()}
                sx={{
                  color: 'white',
                  backgroundColor: alpha('#fff', 0.1),
                  '&:hover': { backgroundColor: alpha('#fff', 0.2) }
                }}
              >
                <AiOutlineReload />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Paper>

      {/* Summary Cards */}
      {!rataLoading && <SummaryCards />}

      {/* Alert for no RATA records */}
      {!rataLoading && (!rataData?.ratas || rataData.ratas.length === 0) && (
        <Alert severity="info" sx={{ mb: 3 }}>
          No RATA records found. Please add RATA rates for different salary grades to enable proper allowance calculations.
        </Alert>
      )}

      <Paper sx={{ p: 0, borderRadius: 2, overflow: 'hidden' }}>
        <CustomPage
          dataListName="ratas"
          schema={rataSchema}
          title=""
          description=""
          hasEdit={false} // We'll handle edit through additional menu options
          hasDelete={true}
          hasAdd={false} // We'll use custom add element
          additionalMenuOptions={[
            ...additionalMenuOptions,
            ({ row, parentClose }) => (
              <MenuItem
                key="edit-rata"
                onClick={() => {
                  handleEdit(row);
                  parentClose();
                }}
                sx={{ display: "flex", gap: 1 }}
              >
                <AiOutlineEdit />
                Edit RATA
              </MenuItem>
            )
          ]}
          customAddElement={
            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
              <Button
                variant="contained"
                onClick={handleAddNew}
                startIcon={<AiOutlinePlus />}
                sx={{
                  background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
                  boxShadow: `0 3px 5px 2px ${alpha(theme.palette.primary.main, 0.3)}`,
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: `0 6px 10px 4px ${alpha(theme.palette.primary.main, 0.3)}`
                  }
                }}
              >
                ADD RATA RATE
              </Button>
              <Button
                variant="outlined"
                onClick={handleExport}
                startIcon={<AiOutlineDownload />}
                sx={{
                  borderColor: theme.palette.primary.main,
                  color: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    borderColor: theme.palette.primary.dark
                  }
                }}
              >
                EXPORT
              </Button>
            </Stack>
          }
        />
      </Paper>

      {/* Enhanced Add/Edit Dialog */}
      <EnhancedRataDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        editData={selectedData}
      />

      {/* View Details Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={handleViewDialogClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          }
        }}
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, #264524 0%, #375e38 100%)',
            color: 'white',
            py: 3
          }}
        >
          <Typography variant="subtitle1" fontWeight="bold" component="div">
            RATA Details
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {viewData && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Salary Grade
                  </Typography>
                  <Chip
                    label={`SG-${viewData.SG}`}
                    color="primary"
                    variant="outlined"
                    icon={<MdGrade />}
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Monthly RATA
                </Typography>
                <Typography variant="h6" color="success.main" fontWeight="bold">
                  ₱{(viewData.RATA || 0).toLocaleString()}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Annual RATA
                </Typography>
                <Typography variant="h6" color="success.main" fontWeight="bold">
                  ₱{((viewData.RATA || 0) * 12).toLocaleString()}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Created Date
                </Typography>
                <Typography variant="body1">
                  {new Date(viewData.createdAt).toLocaleDateString()}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body1">
                  {new Date(viewData.updatedAt).toLocaleDateString()}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleViewDialogClose} variant="outlined">
            Close
          </Button>
          <Button
            onClick={() => {
              handleViewDialogClose();
              handleEdit(viewData);
            }}
            variant="contained"
            startIcon={<AiOutlineEdit />}
            sx={{
              background: 'linear-gradient(45deg, #264524 30%, #375e38 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1a2f1a 30%, #2a4a2b 90%)',
              }
            }}
          >
            Edit
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RataPage;
